<?php

namespace App\Http\Requests\Customers;

use App\Http\Requests\BaseRequest;

class SearchCustomerRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'team_id' => ['required', 'exists:teams,id'],
            'customer_type' => ['required', 'exists:selections,id'],
            'name' => ['required', 'string', 'max:255'],
            'identity_no' => ['required', 'string', 'max:20'],
        ];

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The Name is required.',
            'name.max' => 'The Name cannot exceed 255 characters.',
            'identity_no.required' => 'The Identity No is required.',
            'identity_no.max' => 'The Identity No cannot exceed 20 characters.',
            'headquarter_id.required' => 'The Headquarter is required.',
            'company_id.required' => 'The Company is required.',
            'team_id.required' => 'The Team Name is required.',
        ];
    }
}
