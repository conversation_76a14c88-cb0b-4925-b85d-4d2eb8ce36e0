<script setup lang="ts">
import FaIcon from '@/components/FaIcon.vue';
import FormSelect from '@/components/form/FormSelect.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/composables/useAuth';
import { computed, watch } from 'vue';

const { isHeadquarter, hasHeadquarterAccess, hasCompanyAccess, hasTeamAccess } = useAuth();

interface Form {
    id?: number;
    headquarter_id: number | null;
    company_id: number | null;
    team_id: number | null;
    selection_customer_type_id: number | null;
    customer_type: string | null;
    selection_type_id: number | null;
    name: string | null;
    identity_no: string | null;
    status: number | null;
    remark: string | null;
    company_name: string | null;
    business_registration_no: string | null;
    property: {
        ownership_no: string | null;
        lot_number: string | null;
        selection_land_category_id: number | null;
        land_category_other: string | null;
        selection_type_of_property_id: number | null;
        selection_land_size_unit: number | null;
        selection_land_status_id: number | null;
        no_syit_piawai: string | null;
        certified_plan_no: string | null;
        built_up_area_of_property: string | null;
        city: string | null;
        location: string | null;
        district: string | null;
        address: {
            line_1: string | null;
            line_2: string | null;
            postcode: string | null;
            city: string | null;
            selection_state_id: number | null;
            state: string | null;
            selection_country_id: number | null;
            country: string | null;
        };
    };
}

interface Selection {
    id: number | null;
    value: string | null;
}

const props = defineProps<{
    form: Form[];
    errors: Record<string, string>;
    addCollateral: () => void;
    removeCollateral: (index: number) => void;
    headquarters: Selection[];
    companies: Selection[];
    teams: Selection[];
    customerTypes: Selection[];
    collateralTypes: Selection[];
    propertyTypes: Selection[];
    squareTypes: Selection[];
    landCategories: Selection[];
    landStatuses: Selection[];
    states: Selection[];
    countries: Selection[];
    ordinal: (n: number) => string;
    isEdit: boolean;
    isFooter?: boolean;
    isAccordion?: boolean;
    goBack: () => void;
    goNext: () => void;
}>();

const selectedCustomerType = computed(() => props.customerTypes.find((type) => type.id === props.form.selection_customer_type_id));

const selectedCollateralType = computed(() => props.collateralTypes.find((type) => type.id === props.form.selection_type_id));

const isBusinessCustomer = computed(() => selectedCustomerType.value?.value === 'Business');

const isOtherCollateral = computed(() => selectedCollateralType.value?.value === 'Other');

watch(
    () => {
        if (Array.isArray(props.form)) {
            return props.form.map((collateral) => collateral.property.address.selection_country_id);
        } else {
            return [props.form.property.address.selection_country_id];
        }
    },
    (newCountryIds, oldCountryIds) => {
        newCountryIds.forEach((newCountryId, index) => {
            if (newCountryId !== oldCountryIds?.[index]) {
                if (Array.isArray(props.form)) {
                    props.form[index].property.address.selection_state_id = null;
                } else {
                    props.form.property.address.selection_state_id = null;
                }
            }
        });
    },
);
</script>

<template>
    <CardContent class="py-4">
        <Accordion v-if="isAccordion" type="single" class="w-full" collapsible>
            <AccordionItem v-for="(collateral, index) in form" :key="index" :value="String(index)" class="mb-1">
                <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                        <FaIcon name="plus" />
                    </span>
                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                        <FaIcon name="minus" />
                    </span>
                    <span class="flex-1 py-2 text-left font-medium">{{ ordinal(index + 1) }} Collateral </span>
                    <template #icon>
                        <Button type="button" @click.stop="removeCollateral(index)" variant="destructive" class="flex items-center gap-1">
                            <FaIcon name="trash" />
                            Delete
                        </Button>
                    </template>
                </AccordionTrigger>
                <Separator />
                <AccordionContent class="p-2">
                    <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                        <!-- Collateral Type -->
                        <div class="col-span-1">
                            <Label :for="`selection_type_id.${index}`" class="text-base">
                                Collateral Type
                                <RequiredIndicator />
                            </Label>
                            <p v-if="isEdit">
                                {{ collateralTypes.find((type) => type.id === collateral.selection_type_id)?.value }}
                            </p>
                            <Select v-else v-model="collateral.selection_type_id" :error="errors[`${index}.selection_type_id`]">
                                <SelectTrigger class="w-full">
                                    <SelectValue placeholder="Select collateral type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="type in collateralTypes" :key="type.id" :value="type.id">
                                        {{ type.value }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                            <div v-if="errors[`${index}.selection_type_id`]" class="mt-1 text-sm text-red-500">
                                {{ errors[`${index}.selection_type_id`] }}
                            </div>
                        </div>
                        <div></div>
                        <div v-if="isBusinessCustomer">
                            <Label :for="`company_name.${index}`" class="text-base">
                                Company Name
                                <RequiredIndicator />
                            </Label>
                            <p v-if="isEdit">{{ form.company_name }}</p>
                            <Input
                                v-else
                                :id="`company_name.${index}`"
                                v-model="form.company_name"
                                :error="form.errors?.company_name"
                                required
                                placeholder="Company Name"
                            />
                            <div v-if="form.errors?.company_name" class="mt-1 text-sm text-red-500">
                                {{ form.errors.company_name }}
                            </div>
                        </div>
                        <div v-if="isBusinessCustomer">
                            <Label :for="`business_registration_no.${index}`" class="text-base">
                                Business Registration No
                                <RequiredIndicator />
                            </Label>
                            <p v-if="isEdit">{{ form.business_registration_no }}</p>
                            <Input
                                v-else
                                :id="`business_registration_no-${index}`"
                                v-model="form.business_registration_no"
                                :error="form.errors?.business_registration_no"
                                required
                                placeholder="Business Registration No"
                            />
                            <div v-if="form.errors?.business_registration_no" class="mt-1 text-sm text-red-500">
                                {{ form.errors.business_registration_no }}
                            </div>
                        </div>
                        <div>
                            <Label :for="`name.${index}`" class="text-base">
                                Owner Name
                                <RequiredIndicator />
                            </Label>
                            <p v-if="isEdit">{{ form.name }}</p>
                            <Input v-else :id="`name-${index}`" v-model="form.name" :error="form.errors?.name" required placeholder="Owner Name" />
                            <div v-if="form.errors?.name" class="mt-1 text-sm text-red-500">
                                {{ form.errors.name }}
                            </div>
                        </div>

                        <div>
                            <Label :for="`identity_no.${index}`" class="text-base">
                                Owner IC
                                <RequiredIndicator />
                            </Label>
                            <p v-if="isEdit">{{ form.identity_no }}</p>
                            <Input
                                v-else
                                :id="`identity_no.${index}`"
                                v-model="form.identity_no"
                                :error="form.errors?.identity_no"
                                required
                                placeholder="Owner IC"
                            />
                            <div v-if="form.errors?.identity_no" class="mt-1 text-sm text-red-500">
                                {{ form.errors.identity_no }}
                            </div>
                        </div>

                        <div v-if="isOtherCollateral" class="col-span-2">
                            <Label :for="`remark.${index}`" class="text-base">
                                Remark
                                <RequiredIndicator />
                            </Label>
                            <Textarea :id="`remark.${index}`" v-model="form.remark" :error="form.errors?.remark" required />
                            <div v-if="form.errors?.remark" class="mt-1 text-sm text-red-500">
                                {{ form.errors.remark }}
                            </div>
                        </div>
                        <template v-else>
                            <div>
                                <Label :for="`property.${index}.ownership_no`" class="text-base">
                                    Ownership No
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.ownership_no`"
                                    v-model="form.property.ownership_no"
                                    :error="form.errors[`property.${index}.ownership_no`]"
                                    placeholder="Ownership No"
                                />
                                <div v-if="form.errors[`property.${index}.ownership_no`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.ownership_no`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.selection_type_of_property_id`" class="text-base">
                                    Type of Property
                                    <RequiredIndicator />
                                </Label>
                                <Select
                                    v-model="form.property.selection_type_of_property_id"
                                    :error="form.errors[`property.${index}.selection_type_of_property_id`]"
                                >
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Select Type of Property" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="propertyType in propertyTypes" :key="propertyType.id" :value="propertyType.id">
                                            {{ propertyType.value }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <div v-if="form.errors[`property.${index}.selection_type_of_property_id`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.selection_type_of_property_id`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.lot_number`" class="text-base">
                                    Lot Number
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.lot_number`"
                                    v-model="form.property.lot_number"
                                    :error="form.errors[`property.${index}.lot_number`]"
                                    placeholder="Lot Number"
                                />
                                <div v-if="form.errors[`property.${index}.lot_number`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.lot_number`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.selection_land_category_id`" class="text-base">
                                    Land Category
                                    <RequiredIndicator />
                                </Label>
                                <Select
                                    v-model="form.property.selection_land_category_id"
                                    :error="form.errors[`property.${index}.selection_land_category_id`]"
                                >
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Select land category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="category in landCategories" :key="category.id" :value="category.id">
                                            {{ category.value }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <Input
                                    v-if="form.property.selection_land_category_id === 22"
                                    class="mt-2"
                                    :id="`property.${index}.land_category_other`"
                                    placeholder="Remark"
                                    v-model="form.property.land_category_other"
                                    :error="form.errors[`property.${index}.land_category_other`]"
                                />
                                <div v-if="form.errors[`property.${index}.selection_land_category_id`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.selection_land_category_id`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.city`" class="text-base">
                                    City
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.city`"
                                    v-model="form.property.city"
                                    :error="form.errors[`property.${index}.city`]"
                                    placeholder="City"
                                />
                                <div v-if="form.errors[`property.${index}.city`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.city`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.location`" class="text-base">
                                    Location
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.location`"
                                    v-model="form.property.location"
                                    :error="form.errors[`property.${index}.location`]"
                                    placeholder="Location"
                                />
                                <div v-if="form.errors[`property.${index}.location`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.location`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.land_size`" class="text-base">
                                    Land Size
                                    <RequiredIndicator />
                                </Label>
                                <div class="flex">
                                    <Input
                                        :id="`property.${index}.land_size`"
                                        v-model="form.property.land_size"
                                        :error="form.errors[`property.${index}.land_size`]"
                                        class="w-full !rounded-r-none"
                                        placeholder="Land Size"
                                    />
                                    <div v-if="form.errors[`property.${index}.land_size`]" class="mt-1 text-sm text-red-500">
                                        {{ form.errors[`property.${index}.land_size`] }}
                                    </div>
                                    <Select
                                        v-model="form.property.selection_land_size_unit"
                                        :error="form.errors[`property.${index}.selection_land_size_unit`]"
                                    >
                                        <SelectTrigger class="bg-cloud w-45 !rounded-l-none">
                                            <SelectValue placeholder="Select land size" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="type in squareTypes" :key="type.id" :value="type.id">
                                                {{ type.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div v-if="form.errors[`property.${index}.selection_land_size_unit`]" class="mt-1 text-sm text-red-500">
                                        {{ form.errors[`property.${index}.selection_land_size_unit`] }}
                                    </div>
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.district`" class="text-base">
                                    District
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.district`"
                                    v-model="form.property.district"
                                    :error="form.errors[`property.${index}.district`]"
                                    placeholder="District"
                                />
                                <div v-if="form.errors[`property.${index}.district`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.district`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.no_syit_piawai`" class="text-base">
                                    No. Syit Piawai
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.no_syit_piawai`"
                                    v-model="form.property.no_syit_piawai"
                                    :error="form.errors[`property.${index}.no_syit_piawai`]"
                                    placeholder="No. Syit Piawai"
                                />
                                <div v-if="form.errors[`property.${index}.no_syit_piawai`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.no_syit_piawai`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.certified_plan_no`" class="text-base"> Certified Plan No </Label>
                                <Input
                                    :id="`property.${index}.certified_plan_no`"
                                    v-model="form.property.certified_plan_no"
                                    :error="form.errors[`property.${index}.certified_plan_no`]"
                                    placeholder="Certified Plan No"
                                />
                                <div v-if="form.errors[`property.${index}.certified_plan_no`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.certified_plan_no`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.selection_land_status_id`" class="text-base">
                                    Land Status
                                    <RequiredIndicator />
                                </Label>
                                <Select
                                    v-model="form.property.selection_land_status_id"
                                    :error="form.errors[`property.${index}.selection_land_status_id`]"
                                >
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Select land status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="status in landStatuses" :key="status.id" :value="status.id">
                                            {{ status.value }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <div v-if="form.errors[`property.${index}.selection_land_status_id`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.selection_land_status_id`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.built_up_area_of_property`" class="text-base"> Built Up Area </Label>
                                <div class="flex">
                                    <Input
                                        :id="`property.${index}.built_up_area_of_property`"
                                        v-model="form.property.built_up_area_of_property"
                                        :error="form.errors[`property.${index}.built_up_area_of_property`]"
                                        class="w-full !rounded-r-none"
                                        placeholder="Built Up Area"
                                    />
                                    <div v-if="form.errors[`property.${index}.built_up_area_of_property`]" class="mt-1 text-sm text-red-500">
                                        {{ form.errors[`property.${index}.built_up_area_of_property`] }}
                                    </div>
                                    <Select
                                        v-model="form.property.selection_built_up_area_unit"
                                        :error="form.errors[`property.${index}.selection_built_up_area_unit`]"
                                    >
                                        <SelectTrigger class="bg-cloud w-45 !rounded-l-none">
                                            <SelectValue placeholder="Unit" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem v-for="type in squareTypes" :key="type.id" :value="type.id">
                                                {{ type.value }}
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div v-if="form.errors[`property.${index}.selection_built_up_area_unit`]" class="mt-1 text-sm text-red-500">
                                        {{ form.errors[`property.${index}.selection_built_up_area_unit`] }}
                                    </div>
                                </div>
                            </div>

                            <div class="col-span-2">
                                <Label :for="`property.${index}.address.line_1`" class="text-base">
                                    Address Line 1
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.address.line_1`"
                                    v-model="form.property.address.line_1"
                                    :error="form.errors[`property.${index}.address.line_1`]"
                                    placeholder="Address Line 1"
                                />
                                <div v-if="form.errors[`property.${index}.address.line_1`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.address.line_1`] }}
                                </div>
                            </div>

                            <div class="col-span-2">
                                <Label :for="`property.${index}.address.line_2`" class="text-base"> Address Line 2 </Label>
                                <Input
                                    :id="`property.${index}.address.line_2`"
                                    v-model="form.property.address.line_2"
                                    :error="form.errors[`property.${index}.address.line_2`]"
                                    placeholder="Address Line 2"
                                />
                                <div v-if="form.errors[`property.${index}.address.line_2`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.address.line_2`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.address.postcode`" class="text-base">
                                    Postcode
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.address.postcode`"
                                    v-model="form.property.address.postcode"
                                    :error="form.errors[`property.${index}.address.postcode`]"
                                    placeholder="Postcode"
                                />
                                <div v-if="form.errors[`property.${index}.address.postcode`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.address.postcode`] }}
                                </div>
                            </div>

                            <div>
                                <Label :for="`property.${index}.address.city`" class="text-base">
                                    City
                                    <RequiredIndicator />
                                </Label>
                                <Input
                                    :id="`property.${index}.address.city`"
                                    v-model="form.property.address.city"
                                    :error="form.errors[`property.${index}.address.city`]"
                                    placeholder="City"
                                />
                                <div v-if="form.errors[`property.${index}.address.city`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.address.city`] }}
                                </div>
                            </div>

                            <div>
                                <FormSelect
                                    :id="`property.${index}.address.selection_state_id`"
                                    label="State"
                                    :model-value="form.property.address.selection_state_id"
                                    @update:model-value="collateral.property.address.selection_state_id = $event"
                                    :options="states"
                                    placeholder="Select state"
                                    :required="true"
                                    :error="errors[`${index}.property.address.selection_state_id`]"
                                    :filter-by-country="true"
                                    :selected-country-id="collateral.property.address.selection_country_id"
                                    labelClass="text-base"
                                />
                            </div>

                            <div>
                                <Label :for="`property.${index}.address.selection_country_id`" class="text-base">
                                    Country
                                    <RequiredIndicator />
                                </Label>
                                <Select
                                    v-model="form.property.address.selection_country_id"
                                    :error="form.errors[`property.${index}.address.selection_country_id`]"
                                >
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Select country" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem v-for="country in countries" :key="country.id" :value="country.id">
                                            {{ country.value }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <div v-if="form.errors[`property.${index}.address.selection_country_id`]" class="mt-1 text-sm text-red-500">
                                    {{ form.errors[`property.${index}.address.selection_country_id`] }}
                                </div>
                            </div>
                        </template>
                    </div>
                </AccordionContent>
            </AccordionItem>
        </Accordion>
        <template v-else>
            <Label class="text-[20px]">Collateral Info</Label>
            <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                <!-- Headquarters Selection -->
                <div v-if="!hasHeadquarterAccess">
                    <Label for="headquarter_id" class="text-base">
                        Headquarter Name
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">
                        {{ headquarters.find((headquarter) => headquarter.id === form.headquarter_id)?.display_name }}
                    </p>
                    <Select v-else v-model="form.headquarter_id" :error="form.errors.headquarter_id" class="w-full">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Headquarter Name" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="headquarter in headquarters" :key="headquarter.value" :value="headquarter.value" required>
                                {{ headquarter.label }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                    <div v-if="form.errors.headquarter_id" class="mt-1 text-sm text-red-500">
                        {{ form.errors.headquarter_id }}
                    </div>
                </div>
                <!-- Company Selection -->
                <div v-if="!hasCompanyAccess && !isHeadquarter">
                    <Label for="company_id" class="text-base">
                        Company Name
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ companies.find((company) => company.id === form.company_id)?.display_name }}</p>
                    <Select v-else v-model="form.company_id" :error="form.errors.company_id" class="w-full">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Company Name" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="company in companies" :key="company.value" :value="company.value" required>
                                {{ company.label }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                    <div v-if="form.errors.company_id" class="mt-1 text-sm text-red-500">
                        {{ form.errors.company_id }}
                    </div>
                </div>
                <!-- Team Selection -->
                <div v-if="!hasTeamAccess">
                    <Label for="team_id" class="text-base">
                        Team Name
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ teams.find((team) => team.id === form.team_id)?.name }}</p>
                    <Select v-else v-model="form.team_id" :error="form.errors.team_id" class="w-full">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Team Name" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="team in teams" :key="team.value" :value="team.value" required>
                                {{ team.label }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                    <div v-if="form.errors.team_id" class="mt-1 text-sm text-red-500">
                        {{ form.errors.team_id }}
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                <!-- Collateral Type -->
                <div class="col-span-1">
                    <Label for="selection_customer_type_id" class="py-3 text-base"
                        >Customer Type
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ customerTypes.find((type) => type.id === form.selection_customer_type_id)?.value }}</p>
                    <RadioGroup v-else v-model="form.selection_customer_type_id" :orientation="'horizontal'" class="flex space-x-4">
                        <div v-for="customer in customerTypes" class="flex items-center space-x-2">
                            <RadioGroupItem :value="customer.id" />
                            <Label>{{ customer.value }}</Label>
                        </div>
                    </RadioGroup>
                    <div v-if="form.errors.selection_customer_type_id" class="mt-1 text-sm text-red-500">
                        {{ form.errors.selection_customer_type_id }}
                    </div>
                </div>
                <div></div>
                <div class="col-span-1">
                    <Label for="selection_type_id" class="text-base"
                        >Collateral Type
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ collateralTypes.find((type) => type.id === form.selection_type_id)?.value }}</p>
                    <Select v-else v-model="form.selection_type_id" :error="form.errors.selection_type_id">
                        <SelectTrigger class="w-full">
                            <SelectValue placeholder="Select collateral type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem v-for="type in collateralTypes" :key="type.id" :value="type.id">
                                {{ type.value }}
                            </SelectItem>
                        </SelectContent>
                    </Select>
                    <div v-if="form.errors.selection_type_id" class="mt-1 text-sm text-red-500">
                        {{ form.errors.selection_type_id }}
                    </div>
                </div>
                <div></div>
                <div v-if="isBusinessCustomer">
                    <Label for="company_name" class="text-base"
                        >Company Name
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ form.company_name }}</p>
                    <Input
                        v-else
                        id="company_name"
                        v-model="form.company_name"
                        :error="form.errors.company_name"
                        required
                        placeholder="Company Name"
                    />
                    <div v-if="form.errors.company_name" class="mt-1 text-sm text-red-500">
                        {{ form.errors.company_name }}
                    </div>
                </div>
                <div v-if="isBusinessCustomer">
                    <Label for="business_registration_no" class="text-base"
                        >Business Registration No
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ form.business_registration_no }}</p>
                    <Input
                        v-else
                        id="business_registration_no"
                        v-model="form.business_registration_no"
                        :error="form.errors.business_registration_no"
                        required
                        placeholder="Business Registration No"
                    />
                    <div v-if="form.errors.business_registration_no" class="mt-1 text-sm text-red-500">
                        {{ form.errors.business_registration_no }}
                    </div>
                </div>
                <div>
                    <Label for="name" class="text-base"
                        >Owner Name
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ form.name }}</p>
                    <Input v-else id="name" v-model="form.name" :error="form.errors.name" required placeholder="Owner Name" />
                    <div v-if="form.errors.name" class="mt-1 text-sm text-red-500">
                        {{ form.errors.name }}
                    </div>
                </div>

                <div>
                    <Label for="identity_no" class="text-base"
                        >Owner IC
                        <RequiredIndicator />
                    </Label>
                    <p v-if="isEdit">{{ form.identity_no }}</p>
                    <Input v-else id="identity_no" v-model="form.identity_no" :error="form.errors.identity_no" required placeholder="Owner IC" />
                    <div v-if="form.errors.identity_no" class="mt-1 text-sm text-red-500">
                        {{ form.errors.identity_no }}
                    </div>
                </div>

                <div v-if="isOtherCollateral" class="col-span-2">
                    <Label for="remark" class="text-base"
                        >Remark
                        <RequiredIndicator />
                    </Label>
                    <Textarea id="remark" v-model="form.remark" :error="form.errors.remark" required />
                    <div v-if="form.errors.remark" class="mt-1 text-sm text-red-500">
                        {{ form.errors.remark }}
                    </div>
                </div>
                <template v-else>
                    <div>
                        <Label for="property.ownership_no" class="text-base"
                            >Ownership No
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.ownership_no"
                            v-model="form.property.ownership_no"
                            :error="form.errors['property.ownership_no']"
                            placeholder="Ownership No"
                        />
                        <div v-if="form.errors['property.ownership_no']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.ownership_no'] }}
                        </div>
                    </div>
                    <div>
                        <Label for="property.selection_type_of_property_id" class="text-base"
                            >Type of Property
                            <RequiredIndicator />
                        </Label>
                        <Select v-model="form.property.selection_type_of_property_id" :error="form.errors['property.selection_type_of_property_id']">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Select Type of Property" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="property in propertyTypes" :key="property.id" :value="property.id">
                                    {{ property.value }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        <div v-if="form.errors['property.selection_type_of_property_id']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.selection_type_of_property_id'] }}
                        </div>
                    </div>
                    <div>
                        <Label for="property.lot_number" class="text-base"
                            >Lot Number
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.lot_number"
                            v-model="form.property.lot_number"
                            :error="form.errors['property.lot_number']"
                            placeholder="Lot Number"
                        />
                        <div v-if="form.errors['property.lot_number']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.lot_number'] }}
                        </div>
                    </div>
                    <div>
                        <Label for="property.selection_land_category_id" class="text-base"
                            >Land Category
                            <RequiredIndicator />
                        </Label>
                        <Select v-model="form.property.selection_land_category_id" :error="form.errors['property.selection_land_category_id']">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Select land category" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="category in landCategories" :key="category.id" :value="category.id">
                                    {{ category.value }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        <Input
                            v-if="form.property.selection_land_category_id === 22"
                            class="mt-2"
                            id="property.land_category_other"
                            placeholder="Remark"
                            v-model="form.property.land_category_other"
                            :error="form.errors['property.land_category_other']"
                        />
                        <div v-if="form.errors['property.selection_land_category_id']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.selection_land_category_id'] }}
                        </div>
                    </div>
                    <div>
                        <Label for="property.city" class="text-base"
                            >City
                            <RequiredIndicator />
                        </Label>
                        <Input id="property.city" v-model="form.property.city" :error="form.errors['property.city']" placeholder="City" />
                        <div v-if="form.errors['property.city']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.city'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.location" class="text-base"
                            >Location
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.location"
                            v-model="form.property.location"
                            :error="form.errors['property.location']"
                            placeholder="Location"
                        />
                        <div v-if="form.errors['property.location']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.location'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.land_size" class="text-base"
                            >Land Size
                            <RequiredIndicator />
                        </Label>
                        <div class="flex">
                            <Input
                                id="property.land_size"
                                v-model="form.property.land_size"
                                :error="form.errors['property.land_size']"
                                class="w-full !rounded-r-none"
                                placeholder="Land Size"
                            />
                            <div v-if="form.errors['property.land_size']" class="mt-1 text-sm text-red-500">
                                {{ form.errors['property.land_size'] }}
                            </div>
                            <Select v-model="form.property.selection_land_size_unit" :error="form.errors['property.selection_land_size_unit']">
                                <SelectTrigger class="bg-cloud w-45 !rounded-l-none">
                                    <SelectValue placeholder="Select land size" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="type in squareTypes" :key="type.id" :value="type.id">
                                        {{ type.value }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                            <div v-if="form.errors['property.selection_land_size_unit']" class="mt-1 text-sm text-red-500">
                                {{ form.errors['property.selection_land_size_unit'] }}
                            </div>
                        </div>
                    </div>

                    <div>
                        <Label for="property.district" class="text-base"
                            >District
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.district"
                            v-model="form.property.district"
                            :error="form.errors['property.district']"
                            placeholder="District"
                        />
                        <div v-if="form.errors['property.district']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.district'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.no_syit_piawai" class="text-base"
                            >No. Syit Piawai
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.no_syit_piawai"
                            v-model="form.property.no_syit_piawai"
                            :error="form.errors['property.no_syit_piawai']"
                            placeholder="No. Syit Piawai"
                        />
                        <div v-if="form.errors['property.no_syit_piawai']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.no_syit_piawai'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.certified_plan_no" class="text-base">Certified Plan No</Label>
                        <Input
                            id="property.certified_plan_no"
                            v-model="form.property.certified_plan_no"
                            :error="form.errors['property.certified_plan_no']"
                            placeholder="Certified Plan No"
                        />
                        <div v-if="form.errors['property.certified_plan_no']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.certified_plan_no'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.selection_land_status_id" class="text-base"
                            >Land Status
                            <RequiredIndicator />
                        </Label>
                        <Select v-model="form.property.selection_land_status_id" :error="form.errors['property.selection_land_status_id']">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Select land status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="status in landStatuses" :key="status.id" :value="status.id">
                                    {{ status.value }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        <div v-if="form.errors['property.selection_land_status_id']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.selection_land_status_id'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.built_up_area_of_property" class="text-base">Built Up Area </Label>
                        <div class="flex">
                            <Input
                                id="property.built_up_area_of_property"
                                v-model="form.property.built_up_area_of_property"
                                :error="form.errors['property.built_up_area_of_property']"
                                class="w-full !rounded-r-none"
                                placeholder="Built Up Area"
                            />
                            <div v-if="form.errors['property.built_up_area_of_property']" class="mt-1 text-sm text-red-500">
                                {{ form.errors['property.built_up_area_of_property'] }}
                            </div>
                            <Select
                                v-model="form.property.selection_built_up_area_unit"
                                :error="form.errors['property.selection_built_up_area_unit']"
                            >
                                <SelectTrigger class="bg-cloud w-45 !rounded-l-none">
                                    <SelectValue placeholder="Select Built Up Area" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem v-for="type in squareTypes" :key="type.id" :value="type.id">
                                        {{ type.value }}
                                    </SelectItem>
                                </SelectContent>
                            </Select>
                            <div v-if="form.errors['property.selection_built_up_area_unit']" class="mt-1 text-sm text-red-500">
                                {{ form.errors['property.selection_built_up_area_unit'] }}
                            </div>
                        </div>
                    </div>

                    <div class="col-span-2">
                        <Label for="property.address.line_1" class="text-base"
                            >Address Line 1
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.address.line_1"
                            v-model="form.property.address.line_1"
                            :error="form.errors['property.address.line_1']"
                            placeholder="Address Line 1"
                        />
                        <div v-if="form.errors['property.address.line_1']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.address.line_1'] }}
                        </div>
                    </div>

                    <div class="col-span-2">
                        <Label for="property.address.line_2" class="text-base">Address Line 2</Label>
                        <Input
                            id="property.address.line_2"
                            v-model="form.property.address.line_2"
                            :error="form.errors['property.address.line_2']"
                            placeholder="Address Line 2"
                        />
                        <div v-if="form.errors['property.address.line_2']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.address.line_2'] }}
                        </div>
                    </div>

                    <div>
                        <Label for="property.address.postcode" class="text-base"
                            >Postcode
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.address.postcode"
                            v-model="form.property.address.postcode"
                            :error="form.errors['property.address.postcode']"
                            placeholder="Postcode"
                        />
                        <div v-if="form.errors['property.address.postcode']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.address.postcode'] }}
                        </div>
                    </div>

                    <!-- City -->
                    <div>
                        <Label for="property.address.city" class="text-base"
                            >City
                            <RequiredIndicator />
                        </Label>
                        <Input
                            id="property.address.city"
                            v-model="form.property.address.city"
                            :error="form.errors['property.address.city']"
                            placeholder="City"
                        />
                        <div v-if="form.errors['property.address.city']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.address.city'] }}
                        </div>
                    </div>

                    <!-- State Selection -->
                    <div>
                        <FormSelect
                            id="property.address.selection_state_id"
                            label="State"
                            :model-value="form.property.address.selection_state_id"
                            @update:model-value="form.property.address.selection_state_id = $event"
                            :options="states"
                            placeholder="Select state"
                            :error="form.errors['property.address.selection_state_id']"
                            :filter-by-country="true"
                            :selected-country-id="form.property.address.selection_country_id"
                            labelClass="text-base"
                        />
                    </div>

                    <div>
                        <Label for="property.address.selection_country_id" class="text-base"
                            >Country
                            <RequiredIndicator />
                        </Label>
                        <Select v-model="form.property.address.selection_country_id" :error="form.errors['property.address.selection_country_id']">
                            <SelectTrigger class="w-full">
                                <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem v-for="country in countries" :key="country.id" :value="country.id">
                                    {{ country.value }}
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        <div v-if="form.errors['property.address.selection_country_id']" class="mt-1 text-sm text-red-500">
                            {{ form.errors['property.address.selection_country_id'] }}
                        </div>
                    </div>
                </template>
            </div>
        </template>
    </CardContent>
    <CardFooter v-if="isFooter" class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
        <Button variant="outline" @click="goBack" type="button" class="bg-card text-muted-foreground flex items-center gap-2">
            <FaIcon name="chevron-left" />
            Back
        </Button>
        <Button variant="outline" @click="goNext" type="button" class="bg-green flex items-center gap-2 text-white">
            Next
            <FaIcon name="chevron-right" />
        </Button>
    </CardFooter>
</template>
