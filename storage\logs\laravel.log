[2025-07-30 05:33:35] local.INFO: select 
                SUM(loan_txns.amount) - SUM(loan_payment_details.amount) AS postage_charges_amount
             from `loan_txns` left join `loan_payment_details` on `loan_txns`.`id` = `loan_payment_details`.`loan_txn_id` where `loan_id` = ? and `loan_txn_type_id` = ? and `status` in (?, ?, ?) and `loan_txns`.`deleted_at` is null  
[2025-07-30 05:34:36] local.INFO: select 
                SUM(loan_txns.amount) - SUM(loan_payment_details.amount) AS postage_charges_amount
             from `loan_txns` left join `loan_payment_details` on `loan_txns`.`id` = `loan_payment_details`.`loan_txn_id` where `loan_id` = ? and `loan_txn_type_id` = ? and `status` in (?, ?, ?) and `loan_txns`.`deleted_at` is null  
