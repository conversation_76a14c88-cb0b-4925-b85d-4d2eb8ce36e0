[2025-07-29 17:53:57] local.ERROR: Undefined constant App\Enums\AccessControl\PermissionName::PENDING_REVIEW_UPDATE_LOANS {"userId":4,"exception":"[object] (Error(code: 0): Undefined constant App\\Enums\\AccessControl\\PermissionName::PENDING_REVIEW_UPDATE_LOANS at C:\\Users\\<USER>\\Documents\\app\\las-be\\app\\Policies\\Loans\\LoanPolicy.php:58)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(844): App\\Policies\\Loans\\LoanPolicy->update(Object(App\\Models\\User), Object(App\\Models\\Loan))
#1 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(797): Illuminate\\Auth\\Access\\Gate->callPolicyMethod(Object(App\\Policies\\Loans\\LoanPolicy), 'update', Object(App\\Models\\User), Array)
#2 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(553): Illuminate\\Auth\\Access\\Gate->Illuminate\\Auth\\Access\\{closure}(Object(App\\Models\\User), Object(App\\Models\\Loan))
#3 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(448): Illuminate\\Auth\\Access\\Gate->callAuthCallback(Object(App\\Models\\User), 'update', Array)
#4 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(411): Illuminate\\Auth\\Access\\Gate->raw('update', Array)
#5 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php(398): Illuminate\\Auth\\Access\\Gate->inspect('update', Object(App\\Models\\Loan))
#6 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Auth\\Access\\AuthorizesRequests.php(25): Illuminate\\Auth\\Access\\Gate->authorize('update', Object(App\\Models\\Loan))
#7 C:\\Users\\<USER>\\Documents\\app\\las-be\\app\\Http\\Controllers\\Loans\\LoanController.php(909): App\\Http\\Controllers\\Controller->authorize('update', Object(App\\Models\\Loan))
#8 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Loans\\LoanController->edit(Object(App\\Models\\Loan))
#9 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('edit', Array)
#10 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Loans\\LoanController), 'edit')
#11 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#12 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#13 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(100): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\app\\las-be\\app\\Http\\Middleware\\HandleAppearance.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Documents\\app\\las-be\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}
"} 
[2025-07-30 03:23:58] local.INFO: Invalid sort field:   
[2025-07-30 03:24:31] local.INFO: Invalid sort field:   
[2025-07-30 03:24:58] local.INFO: Invalid sort field:   
[2025-07-30 03:25:09] local.INFO: Invalid sort field:   
[2025-07-30 03:25:13] local.INFO: Invalid sort field:   
[2025-07-30 03:25:16] local.INFO: Invalid sort field:   
[2025-07-30 03:26:42] local.INFO: Invalid sort field:   
[2025-07-30 03:26:51] local.INFO: Invalid sort field:   
[2025-07-30 03:26:56] local.INFO: Invalid sort field:   
[2025-07-30 03:26:59] local.INFO: Invalid sort field:   
[2025-07-30 03:27:03] local.INFO: Invalid sort field:   
[2025-07-30 03:27:09] local.INFO: Invalid sort field:   
[2025-07-30 03:27:12] local.INFO: Invalid sort field:   
[2025-07-30 03:27:16] local.INFO: Invalid sort field:   
[2025-07-30 03:27:20] local.INFO: Invalid sort field:   
[2025-07-30 03:27:22] local.INFO: Invalid sort field:   
[2025-07-30 03:27:54] local.INFO: Invalid sort field:   
[2025-07-30 03:28:01] local.INFO: Invalid sort field:   
[2025-07-30 03:28:27] local.INFO: Invalid sort field:   
[2025-07-30 03:49:10] local.INFO: Invalid sort field:   
[2025-07-30 03:49:19] local.INFO: Invalid sort field:   
[2025-07-30 03:49:21] local.INFO: Invalid sort field:   
[2025-07-30 03:49:52] local.INFO: Invalid sort field:   
[2025-07-30 03:49:55] local.INFO: Invalid sort field:   
[2025-07-30 03:49:57] local.INFO: Invalid sort field:   
[2025-07-30 03:49:59] local.INFO: Invalid sort field:   
[2025-07-30 03:50:06] local.INFO: Invalid sort field:   
[2025-07-30 03:50:08] local.INFO: Invalid sort field:   
[2025-07-30 03:50:10] local.INFO: Invalid sort field:   
[2025-07-30 03:50:16] local.INFO: Invalid sort field:   
[2025-07-30 03:50:46] local.INFO: Invalid sort field:   
[2025-07-30 03:50:49] local.INFO: Invalid sort field:   
[2025-07-30 03:50:51] local.INFO: Invalid sort field:   
[2025-07-30 03:50:52] local.INFO: Invalid sort field:   
[2025-07-30 03:50:58] local.INFO: Invalid sort field:   
[2025-07-30 03:50:59] local.INFO: Invalid sort field:   
[2025-07-30 03:51:10] local.INFO: Invalid sort field:   
[2025-07-30 03:51:25] local.INFO: Invalid sort field:   
[2025-07-30 03:51:27] local.INFO: Invalid sort field:   
[2025-07-30 03:51:40] local.INFO: Invalid sort field:   
[2025-07-30 03:51:41] local.INFO: Invalid sort field:   
[2025-07-30 03:51:47] local.INFO: Invalid sort field:   
[2025-07-30 03:52:55] local.INFO: Invalid sort field:   
[2025-07-30 03:52:59] local.INFO: Invalid sort field:   
[2025-07-30 03:53:03] local.INFO: Invalid sort field:   
[2025-07-30 03:53:06] local.INFO: Invalid sort field:   
[2025-07-30 03:53:11] local.INFO: Invalid sort field:   
[2025-07-30 03:53:14] local.INFO: Invalid sort field:   
[2025-07-30 03:53:18] local.INFO: Invalid sort field:   
[2025-07-30 03:53:22] local.INFO: Invalid sort field:   
[2025-07-30 03:53:23] local.INFO: Invalid sort field:   
[2025-07-30 03:53:27] local.INFO: Invalid sort field:   
[2025-07-30 03:53:29] local.INFO: Invalid sort field:   
[2025-07-30 03:53:32] local.INFO: Invalid sort field:   
[2025-07-30 03:53:35] local.INFO: Invalid sort field:   
[2025-07-30 03:55:00] local.INFO: Invalid sort field:   
[2025-07-30 03:55:03] local.INFO: Invalid sort field:   
[2025-07-30 03:55:07] local.INFO: Invalid sort field:   
[2025-07-30 03:55:16] local.INFO: Invalid sort field:   
[2025-07-30 03:55:23] local.INFO: Invalid sort field:   
[2025-07-30 03:55:26] local.INFO: Invalid sort field:   
[2025-07-30 03:55:32] local.INFO: Invalid sort field:   
[2025-07-30 03:55:56] local.INFO: Invalid sort field:   
[2025-07-30 03:56:00] local.INFO: Invalid sort field:   
[2025-07-30 03:56:17] local.INFO: Invalid sort field:   
[2025-07-30 03:56:22] local.INFO: Invalid sort field:   
[2025-07-30 03:56:28] local.INFO: Invalid sort field:   
[2025-07-30 03:56:30] local.INFO: Invalid sort field:   
[2025-07-30 03:56:33] local.INFO: Invalid sort field:   
[2025-07-30 03:56:40] local.INFO: Invalid sort field:   
[2025-07-30 03:57:04] local.INFO: Invalid sort field:   
[2025-07-30 03:57:08] local.INFO: Invalid sort field:   
[2025-07-30 03:58:28] local.INFO: Invalid sort field:   
[2025-07-30 03:58:32] local.INFO: Invalid sort field:   
[2025-07-30 03:58:45] local.INFO: Invalid sort field:   
[2025-07-30 03:58:57] local.INFO: Invalid sort field:   
[2025-07-30 03:58:59] local.INFO: Invalid sort field:   
[2025-07-30 04:20:04] local.ERROR: The process "C:\xampp\php\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1" exceeded the timeout of 60 seconds. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\ProcessTimedOutException(code: 0): The process \"C:\\xampp\\php\\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1\" exceeded the timeout of 60 seconds. at C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\process\\Process.php:1181)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\process\\Process.php(450): Symfony\\Component\\Process\\Process->checkTimeout()
#1 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\process\\Process.php(251): Symfony\\Component\\Process\\Process->wait()
#2 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(180): Symfony\\Component\\Process\\Process->run(Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(91): Illuminate\\Queue\\Listener->runProcess(Object(Symfony\\Component\\Process\\Process), '128')
#4 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListenCommand.php(74): Illuminate\\Queue\\Listener->listen(NULL, 'default', Object(Illuminate\\Queue\\ListenerOptions))
#5 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListenCommand->handle()
#6 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(780): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListenCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Documents\\app\\las-be\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\app\\las-be\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 
[2025-07-30 04:22:51] local.INFO: Invalid sort field:   
[2025-07-30 04:23:10] local.INFO: Invalid sort field:   
