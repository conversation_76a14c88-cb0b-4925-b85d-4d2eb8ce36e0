<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import AppCard from '@/components/card/AppCard.vue';
import SearchCard from '@/components/card/SearchCard.vue';
import DataTable from '@/components/datatable/DataTable.vue';
import FaIcon from '@/components/FaIcon.vue';
import Heading from '@/components/Heading.vue';
import Pagination from '@/components/Pagination.vue';
import ShowingEntries from '@/components/ShowingEntries.vue';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useFormatOptions } from '@/composables/useFormatOptions';
import AppLayout from '@/layouts/AppLayout.vue';
import type { FilterOptions, PaginatedData } from '@/types/table';
import { formatDateTime } from '@/utils/dateUtils';
import { Head, useForm } from '@inertiajs/vue3';
import { useDebounceFn } from '@vueuse/core';
import { computed, ref } from 'vue';

const { formatEnumOptions } = useFormatOptions();

interface Loan {
    id: number;
    uuid: string;
    code: string;
    updated_at: string;
    updated_by: {
        id: number;
        username: string;
    } | null;
    canUpdate?: boolean;
    canView?: boolean;
    canDelete?: boolean;
}

interface Props {
    loans: PaginatedData<Loan>;
    filters: FilterOptions & {
        code?: string;
        name?: string;
        identity_no?: string;
        team_name?: string;
        status?: number;
        loan_commencement_date_from?: Date | null;
        loan_commencement_date_to?: Date | null;
    };
    statuses: Record<number, string>;
    canCreate: boolean;
}

const props = defineProps<Props>();

const form = useForm({});

const searchValue = ref(props.filters.code || '');
const customerName = ref(props.filters.name || '');
const identityNo = ref(props.filters.identity_no || '');
const team = ref(props.filters.team_name || '');
const status = ref(props.filters.status || '');
const loanCommencementDateFrom = ref(props.filters.loan_commencement_date_from || '');
const loanCommencementDateTo = ref(props.filters.loan_commencement_date_to || '');

const debouncedSearch = useDebounceFn(() => {
    form.get(
        route('loans.index', {
            code: searchValue.value,
            name: customerName.value,
            identity_no: identityNo.value,
            team_name: team.value,
            loan_commencement_date_from: loanCommencementDateFrom.value ? formatDateTime(loanCommencementDateFrom.value, 'YYYY-MM-DD') : null,
            loan_commencement_date_to: loanCommencementDateTo.value ? formatDateTime(loanCommencementDateTo.value, 'YYYY-MM-DD') : null,
            status: status.value,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
}, 300);

const handleSearch = () => {
    debouncedSearch();
};

const formattedLoans = computed(() => {
    const customersList = props.loans.data || [];

    return customersList.map((loan) => {
        const customers = loan.customer || [];
        const name = customers.map((c) => c.name).join(', ');
        const identityNo = customers.map((c) => c.identity_no).join(', ');
        const contacts = customers
            .flatMap((c) => (Array.isArray(c.contacts) ? c.contacts : []))
            .map((contact) => contact.contact_number)
            .join(', ');

        return {
            ...loan,
            name,
            identityNo,
            contacts,
        };
    });
});

const handleReset = () => {
    searchValue.value = '';
    form.get(route('loans.index'));
};

const handleView = (loan: Loan) => {
    form.get(route('loans.show', loan.id));
};

const handleEdit = (loan: Loan) => {
    form.get(route('loans.edit', loan.id));
};

const handlePaginate = (url: string) => {
    form.get(url);
};

const handleSort = (field: string) => {
    const direction = props.filters.sort_field === field && props.filters.sort_direction === 'asc' ? 'desc' : 'asc';

    form.get(
        route('loans.index', {
            ...props.filters,
            sort_field: field,
            sort_direction: direction,
        }),
        {
            preserveState: true,
            preserveScroll: true,
            replace: true,
        },
    );
};

const columns = [
    { field: 'code', label: 'Loan No.', sortable: true, width: 'w-40' },
    { field: 'name', label: 'Borrower', sortable: true, width: 'w-40' },
    { field: 'identityNo', label: 'IC/Business Registration No.', sortable: false, width: 'w-40' },
    {
        field: 'team',
        label: 'Team Name',
        sortable: true,
        width: 'w-40',
    },
    {
        field: 'commencement_date',
        label: 'Loan Commencement Date',
        sortable: true,
        width: 'w-40',
    },
    { field: 'loan_principle_amount', label: 'Principle Amount (RM)', sortable: false, width: 'w-30' },
    { field: 'status', label: 'Status', sortable: true, width: 'w-30' },
    {
        field: 'created_at',
        label: 'Created At',
        sortable: false,
        width: 'w-40',
        format: (value) => formatDateTime(value),
    },
    {
        field: 'created_by.name',
        label: 'Created By',
        sortable: false,
        width: 'w-40',
    },
    {
        field: 'updated_at',
        label: 'Updated At',
        sortable: false,
        width: 'w-40',
        format: (value) => formatDateTime(value),
    },
    {
        field: 'updated_by.name',
        label: 'Updated by',
        sortable: false,
        width: 'w-40',
    },
    { field: 'actions', label: 'Action', sortable: false, sticky: true, width: 'w-[50px]', align: 'text-center' },
];

const sortState = computed(() => ({
    field: props.filters.sort_field || null,
    direction: (props.filters.sort_direction || 'asc') as 'asc' | 'desc',
}));

const statusLabel = (status: number) => {
    const labels = [
        'Draft', // 0
        'Pending Process', // 1
        'Pending Review', // 2
        'Pending Approval', // 3
        'Rejected', // 4
        'Approved', // 5
        'Customer Rejected', // 6
        'Customer Accepted', // 7
        'On-going', // 8
        'On-going (Overdue)', // 9
        'Completed', // 10
        'Cancelled', // 11
    ];
    return labels[status] ?? 'Unknown';
};
</script>

<template>
    <AppLayout>
        <Head title="Loans" />

        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000010" />

            <SearchCard
                v-model:searchValue="searchValue"
                searchLabel="Loan No."
                searchPlaceholder="Loan No."
                v-model:searchStatus="status"
                :status="true"
                :statusOptions="formatEnumOptions(props.statuses)"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #additional-filters>
                    <div class="w-full">
                        <Label class="pb-2" for="borrower">Borrower</Label>
                        <Input id="borrower" placeholder="Borrower" v-model="customerName" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="identityNo">IC/Business Registration No.</Label>
                        <Input id="identityNo" placeholder="IC/Business Registration No." v-model="identityNo" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="team">Team Name</Label>
                        <Input id="team" placeholder="Team Name" v-model="team" class="w-full" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="loan-commencement-date-from">Loan Commencement Date From</Label>
                        <Calendar v-model="loanCommencementDateFrom" placeholderLabel="Loan Commencement Date From" />
                    </div>
                    <div class="w-full">
                        <Label class="pb-2" for="loan-commencement-date-to">Loan Commencement Date To</Label>
                        <Calendar v-model="loanCommencementDateTo" placeholderLabel="Loan Commencement Date To" />
                    </div>
                </template>
            </SearchCard>

            <AppCard :showHeader="false" class="mt-6">
                <div class="mb-4 flex flex-col-reverse items-end justify-end gap-2 sm:flex-row sm:items-center">
                    <Button
                        v-if="props.canCreate"
                        @click="() => form.get(route('loans.create'))"
                        class="bg-teal hover:bg-teal-hover flex items-center gap-2"
                    >
                        <FaIcon name="plus" />
                        Add New Loan
                    </Button>
                </div>
                <div class="mb-4">
                    <DataTable
                        :columns="columns"
                        :data="formattedLoans"
                        :sort-state="sortState"
                        empty-message="No loans found."
                        @sort="handleSort"
                        @view="handleView"
                        @edit="handleEdit"
                        :showDeleteButton="false"
                        :showStatusToggle="false"
                        :showRejectReason="true"
                    >
                        <template #cell-status="{ row }">
                            <Badge
                                :class="[
                                    {
                                        'bg-cement text-black': row.status === 0,
                                        'bg-ocean': row.status === 1,
                                        'bg-canary': row.status === 2,
                                        'bg-orange': row.status === 3,
                                        'bg-chrome': row.status === 4,
                                        'bg-castleton': row.status === 5,
                                        'bg-pink': row.status === 6,
                                        'bg-soften': row.status === 7,
                                        'bg-cobalt': row.status === 8,
                                        'bg-tomato': row.status === 9,
                                        'bg-green': row.status === 10,
                                        'bg-mist': row.status === 11,
                                    },
                                    'text-md px-1 py-0',
                                ]"
                            >
                                {{ statusLabel(row.status) }}
                            </Badge>
                        </template>
                    </DataTable>
                </div>
                <div class="bg-white">
                    <div class="flex items-center justify-between">
                        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                            <div>
                                <ShowingEntries :from="loans.from" :to="loans.to" :total="loans.total" />
                            </div>
                            <div>
                                <Pagination :links="loans.links" @paginate="handlePaginate" />
                            </div>
                        </div>
                    </div>
                </div>
            </AppCard>
        </div>
    </AppLayout>
</template>
