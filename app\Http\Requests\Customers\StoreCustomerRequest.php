<?php

namespace App\Http\Requests\Customers;

use App\Http\Requests\BaseRequest;

class StoreCustomerRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'selection_type_id' => ['required', 'exists:selections,id'],
            'team_id' => ['required', 'exists:teams,id'],
            'display_name' => ['required', 'string', 'max:255'],
            'identity_no' => ['required', 'string', 'max:20'],
            'email' => ['nullable', 'string', 'email', 'max:255'],
            'remark' => ['nullable', 'string', 'max:1000'],

            'contacts.*.selection_type_id' => ['required', 'exists:selections,id'],
            'contacts.*.selection_telephone_country_id' => ['nullable', 'exists:selections,id'],
            'contacts.*.selection_mobile_country_id' => ['nullable', 'exists:selections,id'],
            'contacts.*.telephone' => ['required_without:contacts.*.mobile_phone', 'nullable', 'string', 'max:20'],
            'contacts.*.mobile_phone' => ['required_without:contacts.*.telephone', 'nullable', 'string', 'max:20'],
            'contacts.*.can_receive_sms' => ['required', 'boolean'],
            'contacts.*.is_primary' => ['required', 'boolean'],

            'addresses.*.selection_type_id' => ['required', 'exists:selections,id'],
            'addresses.*.line_1' => ['required', 'string', 'max:255'],
            'addresses.*.line_2' => ['nullable', 'string', 'max:255'],
            'addresses.*.postcode' => ['required', 'numeric', 'digits_between:1,10'],
            'addresses.*.city' => ['required', 'string', 'max:50'],
            'addresses.*.selection_state_id' => ['nullable', 'exists:selections,id'],
            'addresses.*.state' => ['nullable', 'string', 'max:50'],
            'addresses.*.selection_country_id' => ['required', 'exists:selections,id'],
            'addresses.*.country' => ['nullable', 'string', 'max:50'],
            'addresses.*.is_primary' => ['required', 'boolean'],

            'collateral.*.selection_customer_type_id' => ['required', 'exists:selections,id'],
            'collateral.*.selection_type_id' => ['required', 'exists:selections,id'],
            'collateral.*.name' => ['required', 'string', 'max:255'],
            'collateral.*.identity_no' => ['required', 'string', 'max:20'],

            'collateral.*.valuers.*.valuer' => ['required', 'string', 'max:255'],
            'collateral.*.valuers.*.valuation_amount' => ['required', 'regex:/^\d{1,24}(\.\d{1,2})?$/', 'min:0'],
            'collateral.*.valuers.*.valuation_received_date' => ['required', 'date_format:Y-m-d'],
            'collateral.*.valuers.*.land_search_received_date' => ['nullable', 'date_format:Y-m-d'],
            'collateral.*.valuers.*.is_primary' => ['required', 'boolean'],

            'collateral.*.property_owners.*.name' => ['required', 'string', 'max:255'],
            'collateral.*.property_owners.*.identity_no' => ['required', 'string', 'max:20'],
            'collateral.*.property_owners.*.telephone' => ['nullable', 'string', 'max:20'],
            'collateral.*.property_owners.*.mobile_phone' => ['nullable', 'string', 'max:20'],
            'collateral.*.property_owners.*.address.line_1' => ['nullable', 'string', 'max:255'],
            'collateral.*.property_owners.*.address.line_2' => ['nullable', 'string', 'max:255'],
            'collateral.*.property_owners.*.address.postcode' => ['nullable', 'numeric', 'digits_between:1,10'],
            'collateral.*.property_owners.*.address.city' => ['nullable', 'string', 'max:50'],
            'collateral.*.property_owners.*.address.selection_state_id' => ['nullable', 'exists:selections,id'],
            'collateral.*.property_owners.*.address.state' => ['nullable', 'string', 'max:50'],
            'collateral.*.property_owners.*.address.selection_country_id' => ['nullable', 'exists:selections,id'],
            'collateral.*.property_owners.*.address.country' => ['nullable', 'string', 'max:50'],

            'document.*' => ['nullable', 'array'],
            'document.*.file' => ['nullable', 'file', 'max:10240'],

        ];

        if ((int) $this->input('selection_type_id') === 29) {
            $rules += [
                'old_identity_no' => ['nullable', 'string', 'max:20'],
                'registration_date' => ['required', 'date_format:Y-m-d'],
                'years_of_incorporation' => ['required', 'integer', 'min:0'],

                'company.selection_nature_of_business_id' => ['nullable', 'exists:selections,id'],
                'company.selection_country_of_business_id' => ['nullable', 'exists:selections,id'],
                'company.current_paid_up_capital' => ['nullable', 'regex:/^\d{1,24}(\.\d{1,2})?$/'],
                'company.business_turnover' => ['nullable', 'regex:/^\d{1,24}(\.\d{1,2})?$/'],
                'company.business_turnover_date' => ['nullable', 'date_format:Y-m-d'],
                'company.business_net_income' => ['nullable', 'regex:/^\d{1,24}(\.\d{1,2})?$/'],
                'company.business_net_income_date' => ['nullable', 'date_format:Y-m-d'],

                'owners.*.selection_type_id' => ['required', 'exists:selections,id'],
                'owners.*.name' => ['required', 'string', 'max:255'],
                'owners.*.identity_no' => ['required', 'string', 'max:20'],
                'owners.*.selection_nationality_id' => ['required', 'exists:selections,id'],
                'owners.*.share_unit' => ['required', 'numeric', 'min:0', 'max:100'],

                'collateral.*.company_name' => ['required', 'string'],
                'collateral.*.business_registration_no' => ['required', 'string', 'max:100'],
            ];
        } else {
            $rules += [
                'birth_date' => ['required', 'date_format:Y-m-d'],
                'age' => ['required', 'integer', 'min:0'],
                'selection_race_id' => ['required', 'exists:selections,id'],
                'race' => ['nullable', 'string', 'max:50'],
                'selection_gender_id' => ['required', 'exists:selections,id'],
                'gender' => ['nullable', 'string', 'max:50'],
                'selection_marriage_status_id' => ['required', 'exists:selections,id'],
                'marriage_status' => ['nullable', 'string', 'max:50'],
                'selection_nationality_id' => ['required', 'exists:selections,id'],
                'nationality' => ['nullable', 'string', 'max:50'],
                'selection_education_level_id' => ['required', 'exists:selections,id'],
                'education_level' => ['nullable', 'string', 'max:50'],

                'employment.selection_terms_of_employment_id' => ['required', 'exists:selections,id'],
                'employment.term_of_employment' => ['nullable', 'string', 'max:50'],
                'employment.length_service_year' => ['nullable', 'integer', 'min:0'],
                'employment.length_service_month' => ['nullable', 'integer', 'min:0'],
                'employment.job_position' => ['nullable', 'string', 'max:255'],
                'employment.selection_occupation_id' => ['nullable', 'exists:selections,id'],
                'employment.occupation' => ['nullable', 'string', 'max:50'],
                'employment.selection_business_category_id' => ['nullable', 'exists:selections,id'],
                'employment.business_category' => ['nullable', 'string', 'max:50'],
                'employment.gross_income' => ['nullable', 'regex:/^\d{1,24}(\.\d{1,2})?$/', 'min:0'],
                'employment.net_income' => ['nullable', 'regex:/^\d{1,24}(\.\d{1,2})?$/', 'lte:employment.gross_income'],
                'employment.selection_telephone_country_id' => ['nullable', 'exists:selections,id'],
                'employment.selection_mobile_country_id' => ['nullable', 'exists:selections,id'],
                'employment.telephone' => ['nullable', 'string', 'max:20'],
                'employment.mobile_phone' => ['nullable', 'string', 'max:20'],
                'employment.employer_name' => ['nullable', 'string', 'max:255'],
                'employment.address.selection_type_id' => ['nullable', 'exists:selections,id'],
                'employment.address.line_1' => ['nullable', 'string', 'max:255'],
                'employment.address.line_2' => ['nullable', 'string', 'max:255'],
                'employment.address.postcode' => ['nullable', 'numeric', 'digits_between:1,10'],
                'employment.address.city' => ['nullable', 'string', 'max:50'],
                'employment.address.selection_state_id' => ['nullable', 'exists:selections,id'],
                'employment.address.state' => ['nullable', 'string', 'max:50'],
                'employment.address.selection_country_id' => ['nullable', 'exists:selections,id'],
                'employment.address.country' => ['nullable', 'string', 'max:50'],
                'employment.address.is_primary' => ['nullable', 'string', 'max:50'],
            ];
        }

        foreach ($this->input('collateral', []) as $index => $collateral) {
            if (isset($collateral['selection_type_id']) && (int) $collateral['selection_type_id'] === 14) {
                $rules["collateral.$index.property.ownership_no"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.lot_number"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_land_category_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.land_category"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.land_category_other"] = ['nullable', 'string', 'max:100'];
                $rules["collateral.$index.property.selection_type_of_property_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.type_of_property"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.land_size"] = ['required', 'numeric', 'regex:/^\d{1,34}(\.\d{1,2})?$/'];
                $rules["collateral.$index.property.selection_land_size_unit"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.land_size_unit"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_land_status_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.land_status"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.city"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.location"] = ['required', 'string', 'max:255'];
                $rules["collateral.$index.property.district"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.selection_built_up_area_unit"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.built_up_area_unit"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.no_syit_piawai"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.certified_plan_no"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.built_up_area_of_property"] = ['nullable', 'numeric', 'regex:/^\d{1,34}(\.\d{1,2})?$/'];
                $rules["collateral.$index.property.address.line_1"] = ['required', 'string', 'max:255'];
                $rules["collateral.$index.property.address.line_2"] = ['nullable', 'string', 'max:255'];
                $rules["collateral.$index.property.address.postcode"] = ['required', 'numeric', 'digits_between:1,10'];
                $rules["collateral.$index.property.address.city"] = ['required', 'string', 'max:50'];
                $rules["collateral.$index.property.address.selection_state_id"] = ['nullable', 'exists:selections,id'];
                $rules["collateral.$index.property.address.state"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.address.selection_country_id"] = ['required', 'exists:selections,id'];
                $rules["collateral.$index.property.address.country"] = ['nullable', 'string', 'max:50'];
                $rules["collateral.$index.property.address.is_primary"] = ['required', 'boolean'];
            } else {
                $rules["collateral.$index.remark"] = ['required', 'string', 'max:1000'];
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'contacts.*.telephone.required_without' => 'The Contact Number is required',
            'contacts.*.mobile_phone.required_without' => 'The Contact Number is required',
            'collateral.*.property.built_up_area_of_property' => 'The Built Up Area must be numeric with a maximum of 36 digits and up to 2 decimal places.',
            'collateral.*.property.land_size' => 'The Land Size must be numeric with a maximum of 36 digits and up to 2 decimal places.',  
            'employment.net_income' => 'The Net Income must be numeric with a maximum of 24 digits and up to 2 decimal places.',
            'employment.gross_income' => 'The Gross Income must be numeric with a maximum of 24 digits and up to 2 decimal places.',
            'collateral.*.valuers.*.valuation_amount' => 'The Valuation (RM) must be numeric with a maximum of 24 digits and up to 2 decimal places.',
            'company.current_paid_up_capital' => 'The Current Paid Up Capital (RM) must be numeric with a maximum of 24 digits and up to 2 decimal places.',
            'company.business_turnover' => 'The Business Turnover (RM) must be numeric with a maximum of 24 digits and up to 2 decimal places.',
            'company.business_net_income' => 'The Business Net Income (RM) must be numeric with a maximum of 24 digits and up to 2 decimal places.',
            'document.*.file.max' => 'The file is larger than 10MB. Please upload a smaller file.',
        ];
    }

    public function attributes(): array
    {
        return [
            'contacts.*.telephone' => 'Contact Number',
            'contacts.*.mobile_phone' => 'Contact Number',
            'contacts.*.selection_telephone_country_id' => 'Prefix',
            'contacts.*.selection_mobile_country_id' => 'Prefix',
            'selection_type_id' => 'Customer Type',
            'team_id' => 'Team',
            'display_name' => 'Name',
            'identity_no' => 'Identity No',
            'email' => 'Email',
            'remark' => 'Remark',
            'contacts.*.selection_type_id' => 'Type',
            'contacts.*.can_receive_sms' => 'Able SMS',
            'contacts.*.is_primary' => 'Primary Contact',
            'addresses.*.selection_type_id' => 'Address Type',
            'addresses.*.line_1' => 'Address Line 1',
            'addresses.*.line_2' => 'Address Line 2',
            'addresses.*.postcode' => 'Postcode',
            'addresses.*.city' => 'City',
            'addresses.*.selection_state_id' => 'State',
            'addresses.*.state' => 'State',
            'addresses.*.selection_country_id' => 'Country',
            'addresses.*.country' => 'Country',
            'addresses.*.is_primary' => 'Primary',
            'collateral.*.selection_customer_type_id' => 'Collateral Customer Type',
            'collateral.*.selection_type_id' => 'Collateral Type',
            'collateral.*.name' => 'Owner Name',
            'collateral.*.identity_no' => 'Owner Identify No',

            'collateral.*.valuers.*.valuer' => 'Valuer',
            'collateral.*.valuers.*.valuation_amount' => 'Valuation (RM)',
            'collateral.*.valuers.*.valuation_received_date' => 'Valuation Received Date',
            'collateral.*.valuers.*.land_search_received_date' => 'Land Search Received Date',
            'collateral.*.valuers.*.is_primary' => 'Valuers Primary',

            'collateral.*.property_owners.*.name' => 'Name',
            'collateral.*.property_owners.*.identity_no' => 'I/C No',
            'collateral.*.property_owners.*.telephone' => 'Telephone',
            'collateral.*.property_owners.*.mobile_phone' => 'Mobile Phone',
            'collateral.*.property_owners.*.address.line_1' => 'Address Line 1',
            'collateral.*.property_owners.*.address.line_2' => 'Address Line 2',
            'collateral.*.property_owners.*.address.postcode' => 'Postcode',
            'collateral.*.property_owners.*.address.city' => 'City',
            'collateral.*.property_owners.*.address.selection_state_id' => 'State',
            'collateral.*.property_owners.*.address.state' => 'State',
            'collateral.*.property_owners.*.address.selection_country_id' => 'Country',
            'collateral.*.property_owners.*.address.country' => 'Country',
            'document.*' => 'Document',

            'old_identity_no' => 'Old Business Registration No.',
            'registration_date' => 'Date of Registration',
            'years_of_incorporation' => 'Years of Incorporation',
            'company.selection_nature_of_business_id' => 'Nature of Business',
            'company.selection_country_of_business_id' => 'Country of Business Operation',
            'company.current_paid_up_capital' => 'Current Paid Up Capital (RM)',
            'company.business_turnover' => 'Business Turnover (RM)',
            'company.business_turnover_date' => 'Business Turnover Date',
            'company.business_net_income' => 'Business Net Income (RM)',
            'company.business_net_income_date' => 'Business Net Income Date',
            'owners.*.selection_type_id' => 'Owner Type',
            'owners.*.name' => 'Name',
            'owners.*.identity_no' => 'I/C No',
            'owners.*.selection_nationality_id' => 'Nationality',
            'owners.*.share_unit' => 'Share (%)',
            'collateral.*.company_name' => 'Company Name',
            'collateral.*.business_registration_no' => 'Business Registration No',
            'birth_date' => 'Date of Birth',
            'selection_race_id' => 'Race',
            'race' => 'Race',
            'selection_gender_id' => 'Gender',
            'gender' => 'Gender',
            'selection_marriage_status_id' => 'Marital Status',
            'marriage_status' => 'Marital Status',
            'selection_nationality_id' => 'Nationality',
            'nationality' => 'Nationality',
            'selection_education_level_id' => 'Education Level',
            'education_level' => 'Education Level',

            'employment.selection_terms_of_employment_id' => 'Terms of Employment',
            'employment.term_of_employment' => 'Terms of Employment',
            'employment.length_service_year' => 'Length of Service (Year)',
            'employment.length_service_month' => 'Length of Service (Month)',
            'employment.job_position' => 'Job Position',
            'employment.selection_occupation_id' => 'Occupation',
            'employment.occupation' => 'Occupation',
            'employment.selection_business_category_id' => 'Business Classification',
            'employment.business_category' => 'Business Classification',
            'employment.gross_income' => 'Gross Income (Monthly)',
            'employment.net_income' => 'Net Income (Monthly)',
            'employment.selection_telephone_country_id' => 'Prefix of Telephone',
            'employment.selection_mobile_country_id' => 'Prefix of Mobile Phone',
            'employment.telephone' => 'Telephone',
            'employment.mobile_phone' => 'Mobile Phone',
            'employment.employer_name' => 'Employment Name',
            'employment.address.selection_type_id' => 'Address Type',
            'employment.address.line_1' => 'Address Line 1',
            'employment.address.line_2' => 'Address Line 2',
            'employment.address.postcode' => 'Postcode',
            'employment.address.city' => 'City',
            'employment.address.selection_state_id' => 'State',
            'employment.address.state' => 'State',
            'employment.address.selection_country_id' => 'Country',
            'employment.address.country' => 'Country',
            'employment.address.is_primary' => 'Employment Address Primary',

            'collateral.*.property.ownership_no' => 'Ownership No',
            'collateral.*.property.lot_number' => 'Lot Number',
            'collateral.*.property.selection_land_category_id' => 'Land Category',
            'collateral.*.property.land_category' => 'Land Category',
            'collateral.*.property.land_category_other' => 'Land Category of Remark',
            'collateral.*.property.selection_type_of_property_id' => 'Type of Property',
            'collateral.*.property.type_of_property' => 'Type of Property',
            'collateral.*.property.land_size' => 'Land Size',
            'collateral.*.property.selection_land_size_unit' => 'Land Size of Unit',
            'collateral.*.property.land_size_unit' => 'Land Size of Unit',
            'collateral.*.property.selection_land_status_id' => 'Land Status',
            'collateral.*.property.land_status' => 'Land Status',
            'collateral.*.property.city' => 'City',
            'collateral.*.property.location' => 'Location',
            'collateral.*.property.district' => 'District',
            'collateral.*.property.selection_built_up_area_unit' => 'Built Up Area',
            'collateral.*.property.built_up_area_unit' => 'Built Up Area',
            'collateral.*.property.no_syit_piawai' => 'No. Syit Piawai',
            'collateral.*.property.certified_plan_no' => 'Certified Plan No',
            'collateral.*.property.built_up_area_of_property' => 'Built Up Area',
            'collateral.*.property.address.line_1' => 'Address Line 1',
            'collateral.*.property.address.line_2' => 'Address Line 2',
            'collateral.*.property.address.postcode' => 'Postcode',
            'collateral.*.property.address.city' => 'City',
            'collateral.*.property.address.selection_state_id' => 'State',
            'collateral.*.property.address.state' => 'State',
            'collateral.*.property.address.selection_country_id' => 'Country',
            'collateral.*.property.address.country' => 'Country',
            'collateral.*.property.address.is_primary' => 'Address Primary',
            'collateral.*.remark' => 'Remark',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function($validator) {
            $isMalaysia = 25;
            $isMobile = 32;
            $isTelephone = 33;
            $addresses = $this->input('addresses', []);
            $collaterals = $this->input('collateral', []);
            $contacts = $this->input('contacts', []);
             // 1. Validate addresses[*]
             foreach ($addresses as $i => $address) {
                $countryId = (int) ($address['selection_country_id'] ?? 0);
                $stateId = (int) ($address['selection_state_id'] ?? 0);
                if($countryId == $isMalaysia && empty($stateId)){
                        $validator->errors()->add(
                            "addresses.$i.selection_state_id",
                            'The State is required when Country is Malaysia.'
                        );
                }else{
                    if(empty($stateId) && empty($countryId)){
                        $validator->errors()->add(
                            "addresses.$i.selection_state_id",
                            'The State is required.'
                        );
                    }
                }
             }
            // 2. Validate collateral[*].property.address
            foreach ($collaterals as $i => $collateral) {
                // Skip if property or address is not exists
                if (!isset($collateral['property']) || empty($collateral['property'])) {
                    continue; 
                }
                $collateralAddress = $collateral['property']['address'] ?? [];
                $collateralCountryId = (int) ($collateralAddress['selection_country_id'] ?? 0);
                $collateralStateId = (int) ($collateralAddress['selection_state_id'] ?? 0);
                if($collateralCountryId == $isMalaysia && empty($collateralStateId)){
                    $validator->errors()->add(
                        "collateral.$i.property.address.selection_state_id",
                        'The State is required when Country is Malaysia.'
                    );
                }else{
                    if(empty($collateralStateId) && empty($collateralCountryId)){
                        $validator->errors()->add(
                            "collateral.$i.property.address.selection_state_id",
                            'The State is required.'
                        );
                    }
                }
             }

            // 3. Validate contacts[*].selection_telephone_country_id
            
            foreach ($contacts as $i => $contact) {
                if(!isset($contact['selection_type_id']) || empty($contact['selection_type_id'])){
                    continue;
                }
                $contactType = $contact['selection_type_id'];
                //find type of country id in mobile or telephone
                $mobile = (int) ($contact['selection_mobile_country_id'] ?? 0);
                $telephone = (int) ($contact['selection_telephone_country_id'] ?? 0);
                    if($contactType == $isMobile && empty($mobile)){
                        $validator->errors()->add(
                            "contacts.$i.selection_mobile_country_id",
                            'The Prefix is required.'
                        );
                    }
                    if($contactType == $isTelephone && empty($telephone)){
                        $validator->errors()->add(
                            "contacts.$i.selection_telephone_country_id",
                            'The Prefix is required.'
                        );
                    }
            }
        });
    }
} 
